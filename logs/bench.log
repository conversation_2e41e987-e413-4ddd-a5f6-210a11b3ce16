2025-08-11 20:39:47,409 INFO /Users/<USER>/Documents/tg-agents/.venv/bin/bench --version
2025-08-11 20:39:50,612 INFO /Users/<USER>/Documents/tg-agents/.venv/bin/bench find .
2025-08-11 20:40:02,272 INFO /Users/<USER>/Documents/tg-agents/.venv/bin/bench get-app ai_agent
2025-08-11 20:40:02,273 WARNING /Users/<USER>/Documents/tg-agents/.venv/bin/bench get-app ai_agent executed with exit code 1
2025-08-11 20:40:11,448 INFO /Users/<USER>/Documents/tg-agents/.venv/bin/bench init --help
2025-08-11 20:40:25,895 INFO /Users/<USER>/Documents/tg-agents/.venv/bin/bench init frappe-bench --install-app ai_agent
2025-08-11 20:40:25,928 DEBUG cd frappe-bench && python3 -m venv env
2025-08-11 20:40:27,454 DEBUG cd frappe-bench && /Users/<USER>/Documents/tg-agents/frappe-bench/env/bin/python -m pip install --quiet --upgrade pip
2025-08-11 20:40:28,462 DEBUG cd frappe-bench && /Users/<USER>/Documents/tg-agents/frappe-bench/env/bin/python -m pip install --quiet wheel
2025-08-11 20:40:28,906 LOG Getting frappe
2025-08-11 20:40:28,907 DEBUG cd frappe-bench/apps && git clone https://github.com/frappe/frappe.git  --depth 1 --origin upstream
2025-08-11 20:40:34,046 LOG Installing frappe
2025-08-11 20:40:34,069 DEBUG cd /Users/<USER>/Documents/tg-agents/frappe-bench && /Users/<USER>/Documents/tg-agents/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /Users/<USER>/Documents/tg-agents/frappe-bench/apps/frappe 
2025-08-11 20:40:53,995 DEBUG cd /Users/<USER>/Documents/tg-agents/frappe-bench/apps/frappe && yarn install --check-files
2025-08-11 20:40:59,526 ERROR There was a problem while creating frappe-bench
2025-08-11 20:40:59,527 WARNING /Users/<USER>/Documents/tg-agents/.venv/bin/bench init frappe-bench --install-app ai_agent executed with exit code 1
2025-08-11 20:46:19,151 INFO /Library/Frameworks/Python.framework/Versions/3.10/bin/bench
2025-08-11 20:48:51,977 INFO /Library/Frameworks/Python.framework/Versions/3.10/bin/bench
