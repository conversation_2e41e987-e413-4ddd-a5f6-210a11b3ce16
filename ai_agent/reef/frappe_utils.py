# add_seren_to_frappe_min.py
import os, sys
import frappe

SITE_NAME  = "32016-51127.bacloud.info"
BENCH_PATH = "/home/<USER>/frappe-bench"
SITES_PATH = os.path.join(BENCH_PATH, "sites")
APPS_PATH  = os.path.join(B<PERSON>CH_PATH, "apps")
sys.path.extend([BENCH_PATH, APPS_PATH, os.path.join(APPS_PATH, "frappe")])

def _ensure_frappe_ready():
    """Initialize Frappe deterministically regardless of CWD.
    - Discover bench/sites relative to this file.
    - Export FRAPPE_SITE and FRAPPE_SITES_PATH for Frappe internals.
    - Ensure app and site log dirs exist *before* frappe.connect() to prevent logger errors.
    """
    bench_path, sites_path = _discover_bench_and_sites()

    # Export canonical env vars so Frappe resolves paths correctly
    os.environ.setdefault("FRAPPE_SITE", SITE_NAME)
    os.environ.setdefault("FRAPPE_SITES_PATH", sites_path)
    os.environ.setdefault("SITES_HOME", sites_path)  # legacy/compat

    # Stabilize relative path resolution by switching to the bench root
    try:
        os.chdir(bench_path)
    except Exception:
        pass

    # Ensure sys.path contains discovered bench locations
    if bench_path not in sys.path:
        sys.path.append(bench_path)
    apps_path = os.path.join(bench_path, "apps")
    if apps_path not in sys.path:
        sys.path.append(apps_path)

    # --- Ensure log dirs exist BEFORE init/connect ---
    app_logs = os.path.join(bench_path, "apps", "logs")
    os.makedirs(app_logs, exist_ok=True)

    # Site logs path computed from discovered sites path and SITE_NAME
    site_logs = os.path.join(sites_path, SITE_NAME, "logs")
    os.makedirs(site_logs, exist_ok=True)

    # Initialize/connect once. Use discovered sites_path.
    if not getattr(frappe.local, "site", None):
        frappe.init(site=SITE_NAME, sites_path=sites_path)
    if not getattr(frappe.local, "db", None):
        frappe.connect()

if not getattr(frappe.local, "site", None):
	frappe.init(site=SITE_NAME, sites_path=SITES_PATH)
	frappe.connect()

def add_seren_to_frappe(agent: dict) -> dict:
	docname = agent.get("name") or agent.get("agent_name")
	if not docname:
		raise ValueError("agent must include 'agent_name' or 'name'")

	if frappe.db.exists("Agents", docname):
		doc = frappe.get_doc("Agents", docname)
	else:
		doc = frappe.new_doc("Agents")
		doc.name = docname
		doc.flags.name_set_by_user = True
		doc.agent_name = agent.get("agent_name") or docname
		doc.title = agent.get("title")
		doc.telegram_api_hash = agent.get("telegram_api_hash")
		doc.telegram_session_name = agent.get("telegram_session_name")
		doc.telegram_api_id = agent.get("telegram_api_id")
		doc.telegram_phone = agent.get("telegram_phone")
		doc.disabled = 1
		doc.insert(ignore_permissions=True, ignore_if_duplicate=True)
		frappe.db.commit()

	return {"name": doc.name}
