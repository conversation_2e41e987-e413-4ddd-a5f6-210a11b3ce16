#!/bin/bash

# Set Docker context to remote
DOCKER_CONTEXT="remote"
LOCAL_FILE="../ai_agent/reef/frappe_utils.py"
CONTAINER_NAME="c9ac9a15418a"  # Using the first 12 characters of the container ID
CONTAINER_PATH="/home/<USER>/frappe-bench/apps/ai_agent/ai_agent/reef/"

# Check if remote context is available
if ! docker context use $DOCKER_CONTEXT &> /dev/null; then
    echo "Error: Could not switch to Docker context '$DOCKER_CONTEXT'"
    exit 1
fi

# Verify container exists
if ! docker ps -q --no-trunc | grep -q "$CONTAINER_NAME"; then
    echo "Error: Container $CONTAINER_NAME not found in context $DOCKER_CONTEXT"
    echo "Available containers:"
    docker ps -a
    exit 1
fi

# Create directory in container if it doesn't exist
echo "Ensuring directory exists in container..."
docker exec "$CONTAINER_NAME" mkdir -p "$CONTAINER_PATH"

# Copy file to container using docker exec
echo "Copying file to container..."
cat "$LOCAL_FILE" | docker exec -i "$CONTAINER_NAME" bash -c "cat > ${CONTAINER_PATH}frappe_utils.py"

# Switch back to default context
docker context use desktop-linux &> /dev/null

echo "File sync complete!"
